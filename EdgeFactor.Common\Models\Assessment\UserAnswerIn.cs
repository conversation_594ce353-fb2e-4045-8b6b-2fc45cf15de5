﻿namespace EdgeFactor.Common.Models.Assessment
{
    public class UserAnswerIn
    {
        public Guid? Id { get; set; }
        public Guid InstanceId { get; set; }
        public Guid? AssessmentQuestionId { get; set; }
        public int? GradePercentage { get; set; }
        public string? AnswerText { get; set; }
        public Guid? QuestionId { get; set; }
        public Guid? InstanceComponentId { get; set; }
        public int? Marks { get; set; }
        public bool? IsAutoGraded { get; set; }
        public bool? IsEducatorGraded { get; set; }
        public string? Feedback { get; set; }
        public int? AnswerStatusTypeBw { get; set; }

        public IEnumerable<UserQuestionAnswerIn>? UserQuestionAnswers { get; set; }
    }
}
