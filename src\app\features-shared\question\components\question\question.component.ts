import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output, ViewEncapsulation } from '@angular/core';
import { <PERSON><PERSON>anitizer, SafeHtml } from '@angular/platform-browser';
import { IComponent, IEngagementIn, IInstance, IInstanceSection, IParseContentIn, IUserAnswer, IUserAnswerIn, IUserQuestionAnswerIn } from '@app/core/contracts/contract';
import { MatchingQuestionPairDetails } from '@app/core/dtos/MatchingQuestionPairDetails';
import { AnswerStatusType } from '@app/core/enums/answer-status-types';
import { EngagementTypes } from '@app/core/enums/engagment-types.enum';
import { AuthService } from '@app/core/services/auth-service';
import { DataService } from '@app/core/services/data-service';
import { Events } from '@app/core/services/events-service';
import { InstanceService } from '@app/core/services/instance-service';
import { ParseService } from '@app/core/services/parse-service';
import { BaseValueComponent } from '@app/standalone/components/base-control/base-value.component';

import { Subject, debounceTime, takeUntil } from 'rxjs';

export interface QuestionHint {
  hint: string;
  selectedIndexes: number[];
  matchingQuestionPairDetails?: MatchingQuestionPairDetails;
}

@Component({
  selector: 'app-question',
  templateUrl: './question.component.html',
  styleUrls: ['./question.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class QuestionComponent extends BaseValueComponent implements OnInit, OnDestroy {
  @Input() component: IComponent;
  @Input() instance: IInstance;
  @Input() instanceSection: IInstanceSection;
  @Input() instanceComponentValue: string | undefined;
  @Input() isEducator = false;
  @Input() builderPreviewView = false;
  @Input() selectedUserId: string;
  @Input() completed: boolean | '';
  @Input() continuousFeedback: boolean;
  @Output() triggerCompletionCheck = new EventEmitter<any>();
  questionHint: QuestionHint | null;
  instructionText: string | null;
  componentDestroyed$: Subject<boolean> = new Subject();
  showFeedback = false;
  index: number;
  isRiasec = false;
  private oldTitle: string;
  questionTitle: SafeHtml;
  showGradingView = false;
  answerStatusTypes = AnswerStatusType;
  peopleTableSelectedUserFirstName: string;

  constructor(
    private eventService: Events,
    private dataService: DataService,
    private eventsService: Events,
    private instanceService: InstanceService,
    private sanitizer: DomSanitizer,
    private authService: AuthService,
    private parserService: ParseService
  ) {
    super();
  }

  ngOnInit(): void {
    console.log('Question weighting:', this.component?.question?.weighting);
    this.showGradingView = sessionStorage.getItem('showGradingView') === 'true';
    this.peopleTableSelectedUserFirstName = sessionStorage.getItem('peopleTableSelectedUserFirstName') ?? 'User';
    if (this.component.templateField.isViewField !== true) {
      return;
    }

    this.isRiasec = this.component?.question?.questionType?.name?.indexOf('RIASEC') !== -1;
    this.setHint();
    this.setInstructionText();
    this.setData();

    if (this.component.question?.userAnswer?.feedback && this.component.question.userAnswer.feedback.length > 0) {
      this.toggleShowFeedback();
    }

    this.instanceService.questionOrderChanged$.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.index = this.instanceService.getQuestionOrderIndex(this.instanceSection?.sortOrder, this.instanceComponent);
    });

    this.index = this.instanceService.getQuestionOrderIndex(this.instanceSection?.sortOrder, this.instanceComponent);
    this.eventService.subscribe('sectionSubmit', (instanceSectionId: string) => {
      if (this.instanceSection.id === instanceSectionId) {
        this.submitQuestion(null);
      }
    });
  }

  sanitizeHtml(html: string | null): SafeHtml {
    if (!html) {
      return '';
    }

    // Replace all `&nbsp;` with regular spaces before sanitizing
    const sanitizedHtml = html.replace(/&nbsp;/g, ' ');
    return this.sanitizer.bypassSecurityTrustHtml(sanitizedHtml);
  }

  findLastQuestionMarkIndex(word: string): number {
    const regex = /\?(?=[^\?]*$)/g;
    const matches = word.match(regex);

    if (matches && matches.length > 0) {
      return word.lastIndexOf(matches[matches.length - 1]);
    }
    return -1;
  }

  moveQuestionMarkToEnd(sentence: string) {
    const sentenceLength = sentence.length;

    if (sentenceLength === 0) {
      return sentence;
    }

    const index = this.findLastQuestionMarkIndex(sentence);

    if (index === -1) {
      return sentence;
    }

    const nextCharacter = sentence[index + 1];

    if (nextCharacter !== '.' && nextCharacter !== '<' && nextCharacter !== ' ' && nextCharacter !== '&') {
      sentence = `${sentence.substring(0, index)}${sentence.substring(index + 1, index + 2)}?${sentence.substring(index + 2, sentenceLength)}`;
    }
    return sentence;
  }

  override setData() {
    const parseContent = {
      instanceId: this.instance.id,
      rowId: null,
      content: this.component.question.questionText ?? '',
      systemProperties: [],
    } as IParseContentIn;

    this.parserService
      .parse(parseContent)
      .pipe(takeUntil(this.componentDestroyed$))
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      .subscribe((result: any) => {
        if (result?.value) {
          this.questionTitle = this.sanitizeHtml(this.moveQuestionMarkToEnd(result.value));
        }
      });
  }

  someCorrectAnswers() {
    return this.component?.question?.questionAnswers?.some(x => x.isCorrect === true);
  }

  questionAnswerChanged(answer: IUserAnswer, check = false) {
    console.log('questionAnswerChanged called with:', { answer, check });

    if (this.authService.isGuest()) {
      console.log('User is guest, returning early');
      return;
    }

    if (answer.id === '' && this.instanceComponent) {
      console.log('Adding new user answer:', answer);
      this.dataService
        .addUserAnswer(
          {
            ...answer,
            id: null,
            instanceId: this.instance.id && this.instance.id !== '' ? this.instance.id : null,
            questionId: this.component.question?.id,
            instanceComponentId: this.instanceComponent?.id && this.instanceComponent?.id !== '' ? this.instanceComponent.id : null,
            answerStatusTypeBw: this.component.question?.userAnswer?.answerStatusTypeBw,
            userQuestionAnswers: answer.userQuestionAnswers?.map(x => ({ ...x, id: null, userAnswerId: null, instanceId: this.instance.id }) as IUserQuestionAnswerIn),
          } as IUserAnswerIn,
          this.instance.id,
          this.instance.feature.title !== 'RIASEC Interest Profiler'
        )
        .pipe(debounceTime(100), takeUntil(this.componentDestroyed$))
        .subscribe(data => {
          console.log('Add user answer response:', data);
          if (data && this.instanceComponent) {
            this.component.question.userAnswer = data;
            console.log('Updated component question user answer:', this.component.question.userAnswer);

            this.addInstanceSectionComponentEngagement(100);
            this.setHint();

            if (this.isRiasec) {
              console.log('Handling RIASEC specific logic');
              this.component.question.userAnswer.isSubmitted = true;
              this.instanceComponent.completed = true;
              console.log('Updated RIASEC state:', {
                isSubmitted: this.component.question.userAnswer.isSubmitted,
                completed: this.instanceComponent.completed,
              });
              this.checkInstanceComplete();
            }

            if (check === true) {
              console.log('Check is true, calling checkInstanceComplete');
              this.checkInstanceComplete();
            }
          }
        });
    } else if (this.instanceComponent) {
      console.log('Updating existing user answer:', answer);
      this.dataService
        .updateUserAnswer(
          {
            ...answer,
            instanceId: this.instance.id && this.instance.id !== '' ? this.instance.id : null,
            questionId: this.component.question?.id ?? null,
            instanceComponentId: this.instanceComponent.id && this.instanceComponent.id !== '' ? this.instanceComponent.id : null,
            userQuestionAnswers: answer.userQuestionAnswers?.map(x => ({ ...x, id: null }) as IUserQuestionAnswerIn),
          } as IUserAnswerIn,
          this.instance.id,
          this.instance.feature.title !== 'RIASEC Interest Profiler'
        )
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(data => {
          console.log('Update user answer response:', data);
          if (data) {
            this.component.question.userAnswer = data;
            console.log('Updated component question user answer:', this.component.question.userAnswer);

            this.addInstanceSectionComponentEngagement(100);
            this.setHint();

            if (check === true) {
              console.log('Check is true, calling checkInstanceComplete');
              this.checkInstanceComplete();
            }
          }
        });
    }
  }

  addInstanceSectionComponentEngagement(progress: number) {
    this.dataService
      .addInstanceSectionComponentEngagement({
        instanceId: this.instance.id,
        instanceSectionComponentId: this.instanceComponent?.id,
        engagementType: EngagementTypes.Click,
        percentageValue: progress !== undefined ? Math.round(progress) : 0,
        nominalValue: 1,
      } as IEngagementIn)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe();
  }

  shortAnswerInputChanged(event: any) {
    this.component.question.userAnswer = event;
  }

  saveAndSubmitQuestion(event: any) {
    const answer = this.component?.question?.userAnswer;

    if (this.authService.isGuest()) {
      return;
    }

    if (answer && answer.id === '' && this.instanceComponent) {
      this.dataService
        .addUserAnswer(
          {
            ...answer,
            id: null,
            instanceId: this.instance.id && this.instance.id !== '' ? this.instance.id : null,
            questionId: this.component.question?.id,
            instanceComponentId: this.instanceComponent.id && this.instanceComponent.id !== '' ? this.instanceComponent.id : null,
            userQuestionAnswers: answer.userQuestionAnswers?.map(x => ({ ...x, id: null, userAnswerId: null, instanceId: this.instance.id }) as IUserQuestionAnswerIn),
          } as IUserAnswerIn,
          this.instance.id,
          this.instance.feature.title !== 'RIASEC Interest Profiler'
        )
        .pipe(debounceTime(100), takeUntil(this.componentDestroyed$))
        .subscribe(data => {
          if (data && this.instanceComponent) {
            this.component.question.userAnswer = data;
            this.addInstanceSectionComponentEngagement(100);
            this.setHint();

            if (this.isRiasec) {
              this.component.question.userAnswer.isSubmitted = true;
              this.instanceComponent.completed = true;
              this.checkInstanceComplete();
            }

            this.checkInstanceComplete();

            event?.stopPropagation();

            if (this.authService.isGuest()) {
              return;
            }

            this.dataService
              .submitQuestion(this.component.question?.id, this.instance.id, this.instance.feature.title !== 'RIASEC Interest Profiler')
              .pipe(takeUntil(this.componentDestroyed$))
              .subscribe(() => {
                if (this.component?.question?.userAnswer && this.instanceComponent) {
                  this.component.question.userAnswer.isSubmitted = true;
                  this.instanceComponent.completed = true;
                  this.checkInstanceComplete();
                  this.triggerCompletionCheck.next(true);
                }
              });
          }
        });
    } else if (answer && this.instanceComponent) {
      this.dataService
        .updateUserAnswer(
          {
            ...answer,
            instanceId: this.instance.id && this.instance.id !== '' ? this.instance.id : null,
            questionId: this.component.question?.id ?? null,
            instanceComponentId: this.instanceComponent.id && this.instanceComponent.id !== '' ? this.instanceComponent.id : null,
            userQuestionAnswers: answer.userQuestionAnswers?.map(x => ({ ...x, id: null }) as IUserQuestionAnswerIn),
          } as IUserAnswerIn,
          this.instance.id,
          this.instance.feature.title !== 'RIASEC Interest Profiler'
        )
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(data => {
          if (data) {
            this.component.question.userAnswer = data;
            this.addInstanceSectionComponentEngagement(100);
            this.setHint();
            this.checkInstanceComplete();

            event?.stopPropagation();

            if (this.authService.isGuest()) {
              return;
            }

            this.dataService
              .submitQuestion(this.component.question?.id, this.instance.id, this.instance.feature.title !== 'RIASEC Interest Profiler')
              .pipe(takeUntil(this.componentDestroyed$))
              .subscribe(() => {
                if (this.component?.question?.userAnswer && this.instanceComponent) {
                  this.component.question.userAnswer.isSubmitted = true;
                  this.instanceComponent.completed = true;
                  this.checkInstanceComplete();
                  this.triggerCompletionCheck.next(true);
                }
              });
          }
        });
    }
  }

  submitQuestion(event: any) {
    event?.stopPropagation();

    if (this.authService.isGuest()) {
      return;
    }

    this.dataService
      .submitQuestion(this.component.question?.id, this.instance.id, this.instance.feature.title !== 'RIASEC Interest Profiler')
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(() => {
        if (this.component?.question?.userAnswer && this.instanceComponent) {
          this.component.question.userAnswer.isSubmitted = true;
          this.instanceComponent.completed = true;
          this.checkInstanceComplete();
          this.triggerCompletionCheck.next(true);
        }
      });
  }

  unsubmitQuestion(event: any) {
    event?.stopPropagation();

    if (this.authService.isGuest()) {
      return;
    }

    this.dataService
      .unSubmitQuestion(this.component.question?.id, this.instance.id)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(() => {
        if (this.component?.question?.userAnswer) {
          this.component.question.userAnswer.isSubmitted = false;
        }
      });
  }

  setHint() {
    this.questionHint = {
      hint: 'You selected ',
      selectedIndexes: [],
      matchingQuestionPairDetails: this.questionHint?.matchingQuestionPairDetails,
    } as QuestionHint;

    if (
      (this.component.question?.userAnswer?.userQuestionAnswers && (this.component?.question?.questionType?.name === 'True/False' || this.component?.question?.questionType?.name === 'Yes/No')) ||
      ((this.component?.question?.questionType?.name === 'Multiple Choice' || this.component?.question?.questionType?.name === 'Dropdown') &&
        this.component?.question?.userAnswer?.isSubmitted !== true &&
        this.component.question?.userAnswer?.userQuestionAnswers?.length > 0) ||
      (this.component.question?.purpose && this.component.question?.userAnswer?.userQuestionAnswers.length > 0)
    ) {
      this.questionHint = {
        hint: 'You selected ',
        selectedIndexes: [],
      } as QuestionHint;

      if (this.component?.question?.userAnswer?.userQuestionAnswers) {
        this.component.question?.userAnswer?.userQuestionAnswers?.forEach(answer => {
          const index = this.component.question?.questionAnswers.sort((n1, n2) => n1.sortOrder - n2.sortOrder).findIndex(x => x.id === answer.questionAnswerId);
          if (this.questionHint && index !== -1) {
            this.questionHint.selectedIndexes.push(index);
          }
        });
      }
    } else if (
      (this.component?.question?.questionType?.name === 'Multiple Choice' || this.component?.question?.questionType?.name === 'Dropdown') &&
      this.component?.question?.userAnswer?.isSubmitted
    ) {
      this.questionHint.hint = "You've answered this question";
    } else if (
      (this.component?.question?.questionType.name === 'Matching List' ||
        this.component?.question?.questionType.name === 'Picture Matching' ||
        this.component?.question?.questionType.name === 'Text to Picture') &&
      this.questionHint.matchingQuestionPairDetails
    ) {
      if (
        this.questionHint.matchingQuestionPairDetails?.correctPairsCount > 0 &&
        this.questionHint.matchingQuestionPairDetails?.correctPairsCount === this.questionHint.matchingQuestionPairDetails?.totalPairsCount
      ) {
        this.questionHint = { ...this.questionHint, hint: "You've answered this question." } as QuestionHint;
      } else if (this.questionHint.matchingQuestionPairDetails?.correctPairsCount > 0) {
        this.questionHint = { ...this.questionHint, hint: 'You matched ' } as QuestionHint;
      } else {
        this.questionHint = { ...this.questionHint, hint: 'Too bad, that was the wrong answer.' } as QuestionHint;
      }
    } else if (this.component?.question?.userAnswer?.isSubmitted === true && this.component.question?.userAnswer?.answerStatusTypeBw === AnswerStatusType.Correct) {
      this.questionHint = { hint: 'Great job! You answered correctly.' } as QuestionHint;
    } else if (this.component?.question?.userAnswer?.isSubmitted === true && this.component.question?.userAnswer?.answerStatusTypeBw === AnswerStatusType.PartiallyCorrect) {
      this.questionHint = { hint: 'Your answer is partially correct.' } as QuestionHint;
    } else if (this.component?.question?.userAnswer?.isSubmitted === true && this.component.question?.userAnswer?.answerStatusTypeBw === AnswerStatusType.Incorrect && this.someCorrectAnswers()) {
      this.questionHint = { hint: 'Too bad, that was the wrong answer.' } as QuestionHint;
    } else if (this.component?.question?.userAnswer?.isSubmitted === true && this.component?.question?.userAnswer?.userQuestionAnswers.length > 0) {
      const answerId = this.component?.question?.userAnswer?.userQuestionAnswers[0].questionAnswerId;
      const correctAnswerId = this.component?.question?.questionAnswers.find(x => x.isCorrect)?.id;
      if (answerId === correctAnswerId) {
        this.questionHint = { hint: 'Great job! You answered correctly.' } as QuestionHint;
      } else {
        this.questionHint = { hint: 'Too bad, that was the wrong answer.' } as QuestionHint;
      }
    } else {
      this.questionHint = { hint: '' } as QuestionHint;
    }

    if (
      !this.component?.question?.questionAnswers?.some(x => x.isCorrect) &&
      this.component?.question?.questionType?.name !== 'True/False' &&
      this.component?.question?.questionType?.name !== 'Yes/No' &&
      ((!this.component?.question?.userAnswer?.isSubmitted && (!this.questionHint?.selectedIndexes || this.questionHint?.selectedIndexes?.length < 1)) ||
        (this.component?.question?.questionType?.name !== 'Multiple Choice' &&
          this.component?.question?.questionType?.name !== 'Matching List' &&
          this.component?.question?.questionType?.name !== 'Picture Choice' &&
          this.component?.question?.questionType?.name !== 'Picture Matching' &&
          this.component?.question?.questionType?.name !== 'Text to Picture'))
    ) {
      this.questionHint.hint = '';
    }
  }

  setInstructionText() {
    if (this.component.componentType.name === 'File Upload') {
      this.instructionText = 'Upload your answer.';
    } else if (
      this.component?.question?.questionType?.name === 'Multiple Choice' ||
      this.component?.question?.questionType?.name === 'Dropdown' ||
      this.component?.question?.questionType?.name === 'Picture Choice' ||
      this.component?.question?.questionType?.name === 'Picture Matching' ||
      this.component?.question?.questionType?.name === 'Text to Picture' ||
      this.component?.question?.questionType?.name === 'Opinion Scale'
    ) {
      if (this.component?.question?.questionAnswers.filter(x => x.isCorrect === true).length > 1) {
        this.instructionText = 'Select all the correct answer/s.';
      } else {
        this.instructionText = 'Select the correct answer.';
      }
    } else if (this.component?.question?.questionType?.name === 'Matching List') {
      this.instructionText = 'drag and drop to select the matching pairs.';
    } else if (
      this.component?.question?.questionType?.name !== 'Ranking' &&
      this.component?.question?.questionType?.name !== 'Short Answer' &&
      this.component?.question?.questionType?.name !== 'Interest Profiler'
    ) {
      this.instructionText = 'Fill in your answer.';
    }
  }

  updateUserQuestionAnswerFeedback() {
    if (this.component.question?.userAnswer) {
      this.dataService
        .updateUserQuestionAnswerFeedback(this.component.question?.userAnswer?.id, this.component.question?.userAnswer?.feedback ?? '')
        .pipe(takeUntil(this.componentDestroyed$), debounceTime(500))
        .subscribe();
    }
  }

  toggleShowFeedback() {
    this.showFeedback = !this.showFeedback;
  }

  gradeChanged() {
    if (this.component?.question?.userAnswer) {
      this.component.question.userAnswer.isEducatorGraded = true;
      this.questionAnswerChanged(this.component.question?.userAnswer, true);
    }
  }

  statusChanged(answerStatusType?: AnswerStatusType, gradePercentage?: number) {
    console.log('statusChanged called with:', { answerStatusType, gradePercentage });
    if (this.component?.question?.userAnswer && answerStatusType) {
      console.log('Updating answer status type:', answerStatusType);
      this.component.question.userAnswer.isEducatorGraded = true;
      this.component.question.userAnswer.answerStatusTypeBw = answerStatusType;
      this.questionAnswerChanged(this.component.question?.userAnswer, true);
    } else if (this.component?.question?.userAnswer && gradePercentage !== undefined) {
      this.component.question.userAnswer.isEducatorGraded = true;

      this.component.question.userAnswer.gradePercentage = gradePercentage;
      if (gradePercentage > 0 && gradePercentage < 100) {
        console.log('Setting status to PartiallyCorrect');
        this.component.question.userAnswer.answerStatusTypeBw = AnswerStatusType.PartiallyCorrect;
      } else if (gradePercentage === 100) {
        console.log('Setting status to Correct');
        this.component.question.userAnswer.answerStatusTypeBw = AnswerStatusType.Correct;
      } else {
        console.log('Setting status to Incorrect');
        this.component.question.userAnswer.answerStatusTypeBw = AnswerStatusType.Incorrect;
      }
      console.log('Updating answer with:', {
        isEducatorGraded: this.component.question.userAnswer.isEducatorGraded,
        gradePercentage: this.component.question.userAnswer.gradePercentage,
        answerStatusType: this.component.question.userAnswer.answerStatusTypeBw,
      });
      this.questionAnswerChanged(this.component.question?.userAnswer, true);
    }
  }

  checkInstanceComplete() {
    this.eventsService.publish('instanceInProgress', this.instance.id);
    this.eventsService.publish('assessmentReturned', this.component.id);
    this.eventService.publish('checkCompletion', this.selectedUserId);
  }

  matchingQuestionPairDetailsUpdated(value: MatchingQuestionPairDetails) {
    if (this.questionHint) {
      this.questionHint.matchingQuestionPairDetails = value;
    }
    this.setHint();
  }

  getFirstLetter(index: number): string {
    if (this.component.question?.questionAnswers[index]?.value) {
      return this.component.question?.questionAnswers[index].value?.substring(0, 1) ?? '';
    }

    return '';
  }

  ngOnDestroy() {
    this.eventsService.unsubscribe('sectionSubmit');
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
