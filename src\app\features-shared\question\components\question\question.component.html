@if (component?.templateField?.isViewField) {
  <div class="question" [attr.id]="'comp-' + component?.id">
    <div class="question-title-container">
      <h1 class="question-number">{{ index }}.</h1>
      <h1 class="question-title" [innerHtml]="questionTitle"></h1>
    </div>
    @if (isRiasec !== true && component?.question?.questionType?.name !== 'Short Answer') {
      <h4 class="instruction-text fade">{{ instructionText }}</h4>
    }
    <ng-container>
      <app-question-active
        [required]="component?.templateField?.isBlockRequired"
        [completed]="completed"
        [question]="component.question"
        [instanceId]="instance.id"
        [instanceComponentId]="instanceComponent?.id ?? ''"
        [builderPreviewView]="builderPreviewView"
        [isKnowledgeQuestion]="component?.componentType?.name !== 'Assessment Block'"
        [templateField]="component?.templateField"
        (questionAnswerUpdated)="questionAnswerChanged($event)"
        (shortAnswerInputChanged)="shortAnswerInputChanged($event)"
        (matchingQuestionPairDetailsChanged)="matchingQuestionPairDetailsUpdated($event)">
      </app-question-active>
    </ng-container>
    <!-- QUESTION BOTTOM BUTTONS -->
    @if ((component?.question?.userAnswer?.isSubmitted !== true || isEducator === true || component.templateField.isRetake === true) && isRiasec !== true && showGradingView !== true) {
      <ion-card class="question-hint fade" [attr.id]="'hint-' + component?.id">
        <ion-card-content class="card-width">
          <ion-grid>
            <ion-row>
              <ion-col size="12">
                <div class="center-content-bottom">
                  @if (component?.question?.userAnswer?.answerText && component?.question?.userAnswer?.isSubmitted) {
                    @switch (component?.question?.questionType?.name) {
                      @case ('File Upload') {
                        <ion-label class="value"><span class="grey">You've answered this question.</span></ion-label>
                      }
                      @case ('Opinion Scale') {
                        <ion-label class="value"
                          ><span class="grey">You selected </span><strong>{{ component?.question?.userAnswer?.answerText }}</strong
                          >.</ion-label
                        >
                      }
                      @case ('Short Answer') {
                        <ion-label class="value"><span class="grey">You've answered this question.</span></ion-label>
                      }
                      @case ('Interest Ranking') {
                        <ion-label class="value"
                          ><span class="grey">You selected </span><strong>{{ component?.question?.userAnswer?.answerText }}</strong
                          >.</ion-label
                        >
                      }
                      @case ('Interest Profiler') {
                        <ion-label class="value"
                          ><span class="grey">You selected </span><strong>{{ component?.question?.userAnswer?.answerText }}</strong
                          >.</ion-label
                        >
                      }
                      @case ('Riasec Interest Profiler') {
                        @switch (component?.question?.userAnswer?.answerText) {
                          @case ('0') {
                            <ion-label class="value"><span class="grey">You selected </span><strong>Strongly Dislike</strong>.</ion-label>
                          }
                          @case ('1') {
                            <ion-label class="value"><span class="grey">You selected </span><strong>Dislike</strong>.</ion-label>
                          }
                          @case ('2') {
                            <ion-label class="value"><span class="grey">You selected </span><strong>Unsure</strong>.</ion-label>
                          }
                          @case ('3') {
                            <ion-label class="value"><span class="grey">You selected </span><strong>Like</strong>.</ion-label>
                          }
                          @case ('4') {
                            <ion-label class="value"><span class="grey">You selected </span><strong>Strongly Like</strong>.</ion-label>
                          }
                        }
                      }
                    }
                  }
                  @if (questionHint?.hint || questionHint?.selectedIndexes?.length > 0) {
                    <ion-label class="value"
                      ><span class="grey">{{ questionHint?.hint }}</span>
                      @if (questionHint?.selectedIndexes) {
                        @if (component?.question?.questionType?.name === 'True/False' || component?.question?.questionType?.name === 'Yes/No') {
                          <span>{{ getFirstLetter(questionHint.selectedIndexes[0]) }}</span>
                        } @else {
                          @for (selectedIndex of questionHint?.selectedIndexes; track selectedIndex; let i = $index) {
                            <span> {{ i > 0 ? ', ' : '' }}{{ selectedIndex | indexToLetter }}</span>
                          }
                        }
                        @if (!questionHint?.matchingQuestionPairDetails) {
                          <span class="grey">.</span>
                        }
                      }
                      @if (
                        questionHint?.matchingQuestionPairDetails &&
                        questionHint?.matchingQuestionPairDetails?.correctPairsCount > 0 &&
                        questionHint?.matchingQuestionPairDetails?.totalPairsCount > 0 &&
                        questionHint?.matchingQuestionPairDetails?.correctPairsCount < questionHint?.matchingQuestionPairDetails?.totalPairsCount
                      ) {
                        <span>{{ questionHint?.matchingQuestionPairDetails?.correctPairsCount ?? 0 }}</span>
                        <span class="grey"> of the </span>
                        <span>{{ questionHint?.matchingQuestionPairDetails?.totalPairsCount ?? 0 }}</span>
                        <span class="grey"> pairs correctly.</span>
                      }
                    </ion-label>
                  }
                  <div class="btn-container">
                    @if (component?.question?.questionType?.name === 'Short Answer' && component?.question?.userAnswer?.isSubmitted !== true) {
                      <ion-button
                        type="submit"
                        fill="solid"
                        color="primary"
                        size="small"
                        [disabled]="!component?.question?.userAnswer || component?.question?.userAnswer?.answerText === ''"
                        (click)="saveAndSubmitQuestion($event)">
                        <ion-icon slot="end" name="chevron-forward-outline"></ion-icon>Submit
                      </ion-button>
                    } @else if (component?.question?.userAnswer?.isSubmitted !== true) {
                      <ion-button type="submit" fill="solid" color="primary" size="small" [disabled]="!component?.question?.userAnswer" (click)="submitQuestion($event)">
                        <ion-icon slot="end" name="chevron-forward-outline"></ion-icon>Submit
                      </ion-button>
                    } @else if (component?.question?.userAnswer?.isSubmitted === true && (instance?.isRetake === true || component?.templateField?.isRetake === true)) {
                      <ion-button type="submit" fill="solid" color="light" size="small" (click)="unsubmitQuestion($event)">
                        <ion-icon slot="start" name="reload-outline"></ion-icon>Try Again
                      </ion-button>
                    }
                  </div>
                </div>
              </ion-col>
            </ion-row>
          </ion-grid>
        </ion-card-content>
      </ion-card>
    }
    <!-- BUSY GRADING -->
    @else if ((component?.question?.userAnswer?.isSubmitted === true || isEducator === true || component.templateField.isRetake === true) && isRiasec !== true) {
      <ion-card
        class="question-hint fade"
        [ngClass]="{
          correct: component.question.userAnswer?.answerStatusTypeBw === answerStatusTypes.Correct,
          incorrect: component.question.userAnswer?.answerStatusTypeBw === answerStatusTypes.Incorrect,
        }">
        <ion-card-content class="card-width">
          <ion-grid>
            <ion-row>
              <ion-col size="12">
                <div class="center-content-bottom" [attr.id]="'hint-' + component?.id">
                  <ion-label class="value">
                    @if (component?.question?.questionType?.name === 'True/False' || component?.question?.questionType?.name === 'Yes/No') {
                      <span>Did {{ peopleTableSelectedUserFirstName }} answer this question correctly?</span>
                    } @else if (
                      component?.question?.questionType?.name === 'File Upload' ||
                      component?.question?.questionType?.name === 'Multiple Choice' ||
                      component?.question?.questionType?.name === 'Picture Choice' ||
                      component?.question?.questionType?.name === 'Short Answer'
                    ) {
                      <span>Select the grade {{ peopleTableSelectedUserFirstName }} received.</span>
                    }
                  </ion-label>
                  <div class="btn-container">
                    @if (component?.question?.questionType?.name === 'True/False' || component?.question?.questionType?.name === 'Yes/No') {
                      <ion-button
                        type="submit"
                        fill="outline"
                        [color]="component.question.userAnswer?.answerStatusTypeBw !== answerStatusTypes.Incorrect ? 'success' : 'light'"
                        [ngClass]="{
                          'yes-btn': component.question.userAnswer?.answerStatusTypeBw !== answerStatusTypes.Incorrect,
                          selected: component.question.userAnswer?.answerStatusTypeBw === answerStatusTypes.Incorrect,
                        }"
                        size="small"
                        (click)="statusChanged(answerStatusTypes.Correct)">
                        @if (component.question.userAnswer?.answerStatusTypeBw !== answerStatusTypes.Incorrect) {
                          <img class="logo" slot="start" src="/assets/images/Correct Answer Icon.png" alt="Correct" />
                        }
                        <span class="white">Yes</span>
                      </ion-button>
                      <ion-button
                        type="submit"
                        fill="outline"
                        [color]="component.question.userAnswer?.answerStatusTypeBw !== answerStatusTypes.Correct ? 'danger' : 'light'"
                        [ngClass]="{
                          'no-btn': component.question.userAnswer?.answerStatusTypeBw !== answerStatusTypes.Correct,
                          selected: component.question.userAnswer?.answerStatusTypeBw === answerStatusTypes.Correct,
                        }"
                        size="small"
                        (click)="statusChanged(answerStatusTypes.Incorrect)">
                        @if (component.question.userAnswer?.answerStatusTypeBw !== answerStatusTypes.Correct) {
                          <img class="logo" slot="start" src="/assets/images/Incorrect Answer Icon.png" alt="Incorrect" />
                        }
                        <span class="white">No</span>
                      </ion-button>
                    } @else if (
                      component?.question?.questionType?.name === 'File Upload' ||
                      component?.question?.questionType?.name === 'Multiple Choice' ||
                      component?.question?.questionType?.name === 'Picture Choice' ||
                      component?.question?.questionType?.name === 'Short Answer'
                    ) {
                      <div class="btn-container percentage-container">
                        <div class="percentage-buttons">
                          @for (grade of [0, 20, 40, 60, 80, 100]; track grade) {
                            <ion-button
                              type="submit"
                              fill="outline"
                              [color]="component.question.userAnswer?.gradePercentage === grade ? 'warning' : component.question.userAnswer?.answerStatusTypeBw === grade ? 'primary' : 'light'"
                              [ngClass]="{
                                selected: component.question.userAnswer?.answerStatusTypeBw === grade,
                                'grade-percentage': component.question.userAnswer?.gradePercentage === grade,
                              }"
                              size="small"
                              (click)="statusChanged(undefined, grade)">
                              <span class="white">{{ grade }}%</span>
                            </ion-button>
                          }
                        </div>
                      </div>
                    }
                  </div>
                  <div class="right-side-grade">
                    @if (isEducator === true && component.question.userAnswer) {
                      <ion-input class="selected" type="number" [(ngModel)]="component.question.userAnswer.marks" (ngModelChange)="gradeChanged()"></ion-input>
                    }
                    @if (isEducator === true) {
                      <span> / {{ component.question?.weighting ?? 1 }} points </span>
                    }
                  </div>
                </div>
              </ion-col>
            </ion-row>
          </ion-grid>
        </ion-card-content>
      </ion-card>
    }
    <!-- ANSWER TEXT -->
    @if (component?.question?.answerText && component?.question?.userAnswer?.isSubmitted === true) {
      <div class="answerText-container">
        <h6>ANSWER</h6>
        <div class="answerTextRow">
          <ion-icon name="sparkles-outline"></ion-icon>
          <p class="answerText">
            {{ component?.question?.answerText }}
          </p>
          <ion-icon class="swap" name="sparkles-outline"></ion-icon>
        </div>
      </div>
    }
    <!-- FEEDBACK BUTTON -->
    @if (showGradingView === true) {
      <ng-container>
        @if (isEducator === true && component?.question?.userAnswer?.isSubmitted === true && !component?.question?.userAnswer?.feedback && showFeedback !== true) {
          <div class="center-row">
            <ion-button type="submit" fill="clear" color="light" (click)="toggleShowFeedback()"><ion-icon slot="start" name="chatbox-outline"></ion-icon>ADD FEEDBACK</ion-button>
          </div>
        }
        @if (showFeedback === true) {
          <div class="feedback-container">
            <ion-label> Educator Feedback: </ion-label>
            <ion-textarea [disabled]="isEducator !== true" [(ngModel)]="component.question.userAnswer.feedback" (ngModelChange)="updateUserQuestionAnswerFeedback()"></ion-textarea>
          </div>
        }
        @defer (on viewport; prefetch on idle) {
          @if (component?.question?.userAnswer?.isSubmitted === true && isEducator === true && !component.question.userAnswer?.answerStatusTypeBw) {
            <div class="fade">
              <app-results-view-arrow [componentId]="component?.id ?? ''" [id]="'hint-' + component?.id"></app-results-view-arrow>
            </div>
          }
        } @placeholder {
          <div><!-- Arrow will load when visible --></div>
        }
      </ng-container>
    }
  </div>
}
