.question {
  margin-bottom: 50px;

  .instance-title-default {
    margin-left: 0px !important;
  }

  .mat-expansion-panel {
    position: unset !important;
  }

  mat-expansion-panel-header {
    pointer-events: none;
  }

  .retake-button {
    pointer-events: visible;
    z-index: 1000;
  }

  .expansion-icon-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-left: 10px;

    ion-icon {
      pointer-events: none;
    }

    .icon-container {
      pointer-events: visible;
      color: white;
      font-size: 18px;
      height: 18px;
    }
  }

  .dark-background {
    background-color: #181818;
  }

  .button-row {
    // padding: 10px;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
  }

  ::ng-deep .mat-horizontal-stepper-header-container {
    display: none !important;
  }

  ::ng-deep .mat-stepper-horizontal {
    background-color: transparent !important;
  }

  ::ng-deep .mat-horizontal-content-container {
    height: 100%;
    // padding: 0px 16px 16px 16px !important;
    border-radius: 11px;
    padding: 0 2vw 1vw 2vw !important;
  }

  ::ng-deep .mat-horizontal-stepper-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    border-radius: 11px;
  }

  // .instance-title-default {
  //   margin-left: 0px !important;
  //   padding-left: 0px !important;
  // }

  .assessment-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
    padding-top: 30px;
    align-items: center;

    .right-side-heading {
      // margin-right: var(--page-margin-right);
      display: flex;

      .green {
        color: green;
      }

      h3 {
        margin-top: 0px !important;
        margin-bottom: 0px !important;
      }

      h6 {
        text-align: right;
      }

      ion-button {
        margin-left: 18px;
      }
    }
  }

  .mat-expansion-panel {
    background-color: #111;
    color: #8b8b8b;
    margin-bottom: 10px;
    padding: 0px;
    margin: 0 2vw 1vw 2vw;
    border-color: #454545;
    border-width: 2px;
    border-style: solid;
    border-radius: 15px 11px 11px 11px;
    margin-bottom: 40px;
  }

  .mat-expansion-panel-header {
    height: fit-content;
    background:
      linear-gradient(to bottom, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.7) 80%, rgba(0, 0, 0, 0.9) 100%),
      var(--background-image) no-repeat;
    background-size: cover;
  }

  ::ng-deep .mat-expansion-panel-body {
    padding: 0px !important;
    background-color: #111;
  }

  .mat-expansion-panel-header-title {
    color: #8b8b8b !important;
  }

  .mat-expansion-panel-header-description {
    color: #8b8b8b !important;
  }

  ::ng-deep .mat-expansion-panel-header>.mat-expansion-indicator:after {
    color: white;
  }

  .assessment-summary-container {
    border-top: 1px solid #8b8b8b;
    border-bottom: 1px solid #8b8b8b;
    margin-bottom: 20px;
    margin-top: 20px;

    .assessment-summary {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }
  }

  .header-container {
    width: 100%;

    .instance-title-default {
      line-height: 1.1em !important;
      height: 1.1em !important;
    }
  }

  .completed-header-container {
    ion-row {
      z-index: 1006;
      position: absolute;
      top: 0px;
      left: 90px;

      .inner-completed {
        background-color: green;
        padding: 2px;
        font-size: 12px;
        border-radius: 5px;
        color: white;
        margin-right: 16px;
      }
    }
  }

  .parent-container {
    padding-top: 10px;
    position: relative;
  }

  .feedback-container {
    margin-left: var(--page-margin-left);
    margin-right: var(--page-margin-right);
    border-top: 1px solid #8b8b8b;
    padding-top: 20px;

    ion-label {
      color: white;
    }

    ion-textarea {
      border: 1px solid #4e4e4e;
      border-radius: 5px;
      margin-top: 0.5em;
      caret-color: #7f550c;
      color: white;
      font-size: 16px;
      --padding-start: 8px;
      background-color: #181818;
    }
  }

  .center-content-bottom {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    align-content: center;
    padding: 5px;
    min-height: 40px;

    .btn-container {
      margin-right: 20px;
    }

    .percentage-container {
      margin-top: 8px;
      margin-right: 0;
      width: 100%;
    }

    .percentage-buttons {
      display: flex;
      flex-direction: row;
      justify-content: center;
      gap: 5px;
      flex-wrap: wrap;

      ion-button {
        background: rgba(255, 255, 255, 0.05) !important;
        --background: rgba(255, 255, 255, 0.05) !important;
        --background-hover: rgba(255, 255, 255, 0.1) !important;
        --background-activated: rgba(255, 255, 255, 0.15) !important;
        --background-focused: rgba(255, 255, 255, 0.1) !important;

        --border-color: rgba(255, 255, 255, 0.3) !important;
        --border-width: 1px;
        --border-style: solid;
        --box-shadow: none;
        --color: white;

        &.selected {
          --background: rgba(255, 255, 255, 0.1) !important;
          --border-color: rgba(204, 204, 204, 0.8) !important;
        }

        &.grade-percentage {
          --background: rgba(240, 173, 78, 0.2) !important;
          --border-color: rgba(240, 173, 78, 0.8) !important;
          --color: #f99e00 !important;
        }
      }
    }
  }

  .question-hint {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    margin-left: 2.8em;
    margin-top: 0px;

    ion-card-content {
      padding-top: 5px;
      padding-bottom: 5px;
    }

    .value {
      margin-right: 1em;
    }

    .grey {
      color: #aaaaaa;
    }
  }

  .question-title-container {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
  }

  .question-title {
    width: 90%;
    color: #fff;
    font-weight: 700;
  }

  .instruction-text {
    font-style: italic;
    font-size: 16px;
    color: #aaaaaa;
    font-family: 'Roboto';
    margin-left: 2.5em;
    margin-top: 5px;
    margin-bottom: 0px;
  }

  .question-number {
    width: 1.5em;
    color: #aaa;
    font-weight: 900;
  }

  @media (max-width: 960px) {
    .question-title {
      font-size: 18px;
    }

    .instruction-text {
      font-size: 12px;
      margin-left: 3em;
    }

    .question-number {
      font-size: 20px;
      width: 1.75em;
      text-align: center;
    }
  }

  p {
    padding: 0px !important;
    margin: 0px !important;
  }

  .answerText-container {
    margin-left: 2em;

    h6 {
      color: white;
      font-size: 16px;
      text-align: center;
      font-style: italic;
    }

    .answerTextRow {
      display: flex;
      flex-direction: row;
      justify-content: space-evenly;

      ion-icon {
        color: white;
        height: 35px;
        min-width: 35px;
        font-size: 35px;
      }

      .swap {
        transform: rotateY(180deg);
      }

      .answerText {
        font-size: 18px;
        color: #aaaaaa;
        text-align: center;
        margin-right: 20px;
        margin-left: 20px;
      }
    }
  }

  .fade {
    animation: fadeInAnimation ease 3s;
    animation-iteration-count: 1;
    animation-fill-mode: forwards;
  }

  @keyframes fadeInAnimation {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }

  .center-row {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
  }

  .right-side-grade {
    position: absolute;
    right: 0px;
    display: flex;
    flex-direction: row;
    align-items: center;
    align-content: center;
    justify-content: center;
    margin-right: 16px;

    ion-input {
      margin-right: 5px;
      background-color: rgba(255, 255, 255, 0.1);
      margin-top: 0px;
      height: 30px;
      min-height: 30px !important;
      width: 40px;

      border: 1px solid rgba(204, 204, 204, 0.8);
      border-radius: 5px;
      color: white;
      font-size: 16px;
      --padding-start: 8px !important;
      caret-color: #7f550c;
    }

    span {
      color: #aaaaaa;
    }
  }

  .logo {
    width: 1.3em;
    margin-right: 5px;
  }

  /* feedback button styles */
  .yes-btn {
    --background-hover: rgba(40, 167, 69, 0.1);
    --background-activated: rgba(40, 167, 69, 0.2);
    --background-focused: rgba(40, 167, 69, 0.2);
    background-color: rgba(67, 145, 17, 0.4);
    --border-color: rgba(67, 145, 17, 0.8);
    --border-width: 1px;
    --border-style: solid;
    color: #ffffff !important;
    --box-shadow: none;
    margin-right: 5px;
  }

  .no-btn {
    --background-hover: rgba(220, 53, 69, 0.1);
    --background-activated: rgba(220, 53, 69, 0.2);
    --background-focused: rgba(220, 53, 69, 0.2);
    background-color: rgba(254, 51, 36, 0.4);
    --border-color: rgba(254, 51, 36, 0.8);
    --border-width: 1px;
    --border-style: solid;
    color: #ffffff !important;
    --box-shadow: none;
  }

  .white {
    color: #ffffff !important;
  }

  .correct {
    background-color: rgba(67, 145, 17, 0.4);
    border: 1.5px solid rgba(67, 145, 17, 0.8);

    .alphabet {
      background-color: #439111 !important;
      color: white !important;
    }
  }

  .incorrect {
    background-color: rgba(254, 51, 36, 0.4);
    border: 1.5px solid rgba(254, 51, 36, 0.8);

    .alphabet {
      background-color: #fe3324 !important;
      color: white !important;
    }
  }

  .selected {
    background-color: rgba(255, 255, 255, 0.1);
    --border-color: rgba(204, 204, 204, 0.8);
    --border-width: 1px;
    --border-style: solid;
    color: #ffffff !important;
    --box-shadow: none;
  }
}