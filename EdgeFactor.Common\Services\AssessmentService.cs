using System.Text.Json;
using EdgeFactor.Common.DB.Models;
using EdgeFactor.Common.Enums;
using EdgeFactor.Common.Extensions;
using EdgeFactor.Common.Models;
using EdgeFactor.Common.Models.Achievement;
using EdgeFactor.Common.Models.Assessment;
using Microsoft.EntityFrameworkCore;

namespace EdgeFactor.Common.Services
{
    public interface IAssessmentService
    {
        Task<Assessment?> GetAssessment(Guid componentId, Guid? instanceId, bool isEnabled, bool required, bool isBuilder, Guid userId);
        Task<Guid> AddAssessment(AssessmentIn assessment);
        Task<IEnumerable<Question>> SearchQuestions(string query);
        Task<IEnumerable<AssessmentType>> GetAssessmentTypes();
        Task<bool> UpdateAssessment(AssessmentIn assessment);
        Task<bool> UpdateAssessmentQuestion(Guid assessmentId, AssessmentQuestionIn assessmentQuestion);
        Task<PagedResult<AssessmentQuestion>> GetQuestionManagerDashboard(Guid userId, string search, int pageNo, int pageSize);
        Task<IEnumerable<QuestionType>> GetQuestionTypes();
        Task<Question> GetQuestion(Guid questionId);
        Task<AssessmentQuestion> GetAssessmentQuestion(Guid? assessmentQuestionId);
        Task<bool> UpdateQuestion(QuestionIn question, Guid? instanceId);
        Task<UserAnswer?> AddUserAnswer(UserAnswerIn userAnswer, Guid userId);
        Task<UserAnswer?> UpdateUserAnswer(UserAnswerIn userAnswer);
        Task<Guid> CreateQuestion(QuestionIn question, bool isKnowledge = false);
        Task<IEnumerable<Question>> GetQuestionsByIds(IEnumerable<Guid> questionIds, Guid? instanceId, Guid userId, bool isBuilder);
        Task<bool> CheckAssessmentComplete(Guid? assessmentQuestionId, Guid userId, Guid? instanceId);
        Task<bool> SetAssessmentQuestionSortOrderDirect(Guid assessmentId, IEnumerable<AssessmentQuestionLiteIn> assessmentQuestions);
        Task<AssessmentSubmitResult> SubmitAssessment(Guid userId, Guid instanceId);
        //Task<AssessmentSubmitResult> UnSubmitAssessment(Guid userId, Guid instanceId);
        public string GetQuestionTypeName(string componentTypeName);
        Task<UserAnswer> SubmitQuestion(Guid userId, Guid instanceId, Guid questionId);
        Task<UserAnswer> UnSubmitQuestion(Guid userId, Guid instanceId, Guid questionId);
        Task<bool> UpdateUserQuestionFeedback(Guid userQuestionAnswerId, Guid userId, string feedback);
        Task CalculateUserRiasecLetterScore(Guid userId, Guid questionId, int score, Guid? tagId, Guid? userAnswerId, CancellationToken token);
        Task<CombinedUserRiasecFinalScore> GetUserRiasecFinalScore(Guid userId);
        Task CalculateEfDynamicUserRiasecLetterScore(Guid userId, Guid questionId, int score, Guid? tagId, Guid? userAnswerId, CancellationToken token);
        Task ResetRiasecScore(Guid userId, CancellationToken token);
    }
    public class AssessmentService : IAssessmentService
    {
        #region PROPERTIES
        private readonly EdgeFactorContext _db;
        #endregion

        #region CONSTRUCTORS
        public AssessmentService(EdgeFactorContext dbContext)
        {
            _db = dbContext;
        }
        #endregion

        #region METHODS

        public async Task<Assessment?> GetAssessment(Guid componentId, Guid? instanceId, bool isEnabled, bool required, bool isBuilder, Guid userId)
        {
            var assessmentIQueryable = _db.Assessments
                .Include(x => x.AssessmentType)
                .Where(x => x.ComponentId == componentId)
                .AsSplitQuery();

            assessmentIQueryable = assessmentIQueryable
                .GetAssessmentQuestionsInclude(isEnabled, required)
                    .ThenInclude(x => x.Question)
                        .ThenInclude(x => x.QuestionType)
                .GetAssessmentQuestionsInclude(isEnabled, required)
                    .ThenInclude(x => x.Question)
                        .ThenInclude(x => x.QuestionAnswers)
                .AsSplitQuery();

            var assessment = await assessmentIQueryable.FirstOrDefaultAsync();

            if (assessment == null)
            {
                return null;
            }

            var userAssessmentFeedback = await _db.UserAssessmentFeedback.Where(x => x.UserId == userId && x.AssessmentId == assessment.Id).FirstOrDefaultAsync();

            var questionIds = assessment.AssessmentQuestions.Select(x => x.QuestionId).Distinct().ToList();

            var userAnswers = await GetUserAnswers(userId, questionIds, instanceId);

            var tags = await GetInstanceComponentLinkedTags(instanceId);

            if ((tags != null && tags.Any()) || (userAnswers != null && userAnswers.Any()))
            {
                return assessment != null ? new Assessment(assessment, userAnswers.ToList(), instanceId, tags, isBuilder, userAssessmentFeedback) : null;
            }

            return assessment != null ? new Assessment(assessment, instanceId, userAssessmentFeedback) : null;
        }

        public async Task<List<Tags>> GetInstanceComponentLinkedTags(Guid? instanceId)
        {
            var instanceComponentTagsSplit = _db.InstanceSectionComponents
                .Include(x => x.InstanceSection)
                .Include(x => x.Component.ComponentType)
                .Include(x => x.Component.TemplateFields)
                .ThenInclude(y => y.DropDownLinkType)
                .Where(x => x.InstanceSection.InstanceId == instanceId
                            && x.Component.ComponentType.Name == "Dropdown"
                            && x.Component.TemplateFields.DropDownLinkType.Title == "Tags"
                            && x.Component.TemplateFields.IsTag != true)
                .AsSplitQuery();

            var instanceComponentTags = await instanceComponentTagsSplit
                .Select(x => x.Value.ToGUIDNull())
                .ToListAsync();

            var tags = await _db.Tags.Where(x => instanceComponentTags.Contains(x.Id) || instanceComponentTags.Contains(x.ParentId)).Select(x => x.Id).ToListAsync();

            var childTags = await _db.TagLinks
                    .Include(x => x.ChildTag)
                .Where(x => tags.Contains(x.ParentTagId))
                .Select(x => x.ChildTag)
                .ToListAsync();

            return childTags;
        }

        public async Task<bool> CheckAssessmentComplete(Guid? assessmentQuestionId, Guid userId, Guid? instanceId)
        {
            var assessmentId = await _db.AssessmentQuestions.Where(x => x.Id == assessmentQuestionId).Select(x => x.AssessmentId).FirstOrDefaultAsync();

            var assessmentSplit = _db.Assessments
                 .Include(x => x.AssessmentQuestions)
                 .ThenInclude(x => x.Question)
                 .ThenInclude(x => x.QuestionAnswers)
                 .Include(x => x.AssessmentQuestions)
                 .ThenInclude(x => x.Question)
                 .ThenInclude(x => x.QuestionType)
                 .Where(x => x.Id == assessmentId)
                 .AsSplitQuery();

            var assessment = await assessmentSplit
                .FirstOrDefaultAsync();

            if (assessment == null)
            {
                return false;
            }

            var questionIds = assessment.AssessmentQuestions.Select(x => x.QuestionId).Distinct();

            var instanceComponent = await _db.InstanceSectionComponents.Where(x => x.InstanceSection.InstanceId == instanceId && x.ComponentId == assessment.ComponentId).FirstOrDefaultAsync();

            List<Questions>? instanceQuestions = null;

            if (instanceComponent != null && instanceComponent.Value != null && instanceComponent.Value.Length > 0)
            {
                List<InstanceQuestionLite> instanceQuestionsLite = new List<InstanceQuestionLite>();

                if (instanceComponent.Value.IndexOf(";") != -1 && instanceComponent.Value.IndexOf("\"") == -1)
                {
                    instanceQuestionsLite = instanceComponent.Value.Split(';').Select(x => new InstanceQuestionLite() { Id = Guid.Parse(x), SortOrder = 0 }).ToList();
                }
                else if (instanceComponent.Value != null && instanceComponent.Value.Length > 0)
                {
                    instanceQuestionsLite = JsonSerializer.Deserialize<List<InstanceQuestionLite>>(instanceComponent.Value);
                }

                if (instanceQuestionsLite != null && instanceQuestionsLite.Count >= 0)
                {
                    instanceQuestions = await _db.Questions
                        .Include(x => x.QuestionAnswers)
                        .Include(x => x.QuestionType)
                        .Where(x => instanceQuestionsLite.Select(i => i.Id).Contains(x.Id))
                        .ToListAsync();

                    questionIds = questionIds.Concat(instanceQuestionsLite.Select(i => i.Id));
                }
            }

            var userAnswers = await GetUserAnswers(userId, questionIds.ToList(), instanceId);

            return !assessment.AssessmentQuestions.Where(x => userAnswers.Any(u => u.AssessmentQuestionId == x.Id && u.IsSubmitted != true)).Any() && !(instanceQuestions != null && instanceQuestions.Any(i => userAnswers.Any(u => u.QuestionId == i.Id && u.IsSubmitted != true)));
        }

        public async Task<Question> GetQuestion(Guid questionId)
        {
            var question = await _db.Questions
                .Include(x => x.QuestionType)
                .Include(x => x.QuestionAnswers)
                .Where(x => x.Id == questionId)
                .FirstOrDefaultAsync();

            return new Question(question);
        }

        public async Task<AssessmentQuestion> GetAssessmentQuestion(Guid? assessmentQuestionId)
        {
            var question = await _db.AssessmentQuestions
                .Include(x => x.Question)
                    .ThenInclude(x => x.QuestionAnswers)
                .Include(x => x.Question.QuestionType)
                .Where(x => x.Id == assessmentQuestionId)
                .FirstOrDefaultAsync();

            return new AssessmentQuestion(question);
        }

        public async Task<IEnumerable<Question>> GetQuestionsByIds(IEnumerable<Guid> questionIds, Guid? instanceId, Guid userId, bool isBuilder = false)
        {
            var questions = await _db.Questions
                .Include(x => x.QuestionType)
                .Include(x => x.QuestionAnswers)
                .Where(x => questionIds.Contains(x.Id))
            .ToListAsync();

            var userAnswers = await GetUserAnswers(userId, questionIds.ToList(), instanceId);

            var tags = await GetInstanceComponentLinkedTags(instanceId);

            if ((tags != null && tags.Any()) || (userAnswers != null && userAnswers.Any()))
            {
                return questions.Select(x => new Question(x, userAnswers.Where(u => u.QuestionId == x.Id).FirstOrDefault(), instanceId, tags, isBuilder));
            }

            return questions.Select(x => new Question(x));
        }

        public async Task<IEnumerable<Question>> SearchQuestions(string query)
        {
            var questions = _db.Questions
                .Include(x => x.QuestionType)
                .AsQueryable();

            if (query != null)
            {
                questions = questions.Where(x => x.Title.Contains(query) || x.QuestionType.Name.Contains(query) || x.CategoryNavigation.Name.Contains(query));
            }

            var result = await questions.OrderBy(x => x.SortOrder).Select(x => new Question(x, null)).ToListAsync();

            return result;
        }

        public async Task<IEnumerable<AssessmentType>> GetAssessmentTypes()
        {
            var types = await _db.AssessmentTypes.OrderBy(x => x.Name).ToListAsync();
            return types.Select(x => new AssessmentType(x));
        }

        public async Task<IEnumerable<QuestionType>> GetQuestionTypes()
        {
            var types = await _db.QuestionTypes
                .OrderBy(x => x.Name)
                .ToListAsync();

            return types.Select(x => new QuestionType(x));
        }

        public string GetQuestionTypeName(string componentTypeName)
        {
            switch (componentTypeName)
            {
                case "True or False":
                    {
                        return "True/False";
                    }
                case "Yes or No":
                    {
                        return "Yes/No";
                    }
                default:
                    {
                        return componentTypeName;
                    }
            }
        }

        public string GetQuestionComponentTypeName(string componentTypeName)
        {
            switch (componentTypeName)
            {
                case "True/False":
                    {
                        return "True or False";
                    }
                case "Yes/No":
                    {
                        return "Yes or No";
                    }
                default:
                    {
                        return componentTypeName;
                    }
            }
        }

        public async Task<Guid> AddAssessment(AssessmentIn assessment)
        {
            var newAssessment = new Assessments()
            {
                Id = Guid.NewGuid(),
                ComponentId = assessment.ComponentId,
                AssessmentTypeId = assessment.TypeId
            };

            _db.Assessments.Add(newAssessment);

            if (assessment.AssessmentQuestions != null && assessment.AssessmentQuestions.Any())
            {
                foreach (var assessmentQuestion in assessment.AssessmentQuestions)
                {
                    var newQuestion = new Questions()
                    {
                        Id = Guid.NewGuid(),
                        QuestionTypeId = assessmentQuestion.Question.QuestionTypeId,
                        Purpose = assessmentQuestion.Question.Purpose
                    };

                    _db.Questions.Add(newQuestion);

                    _db.AssessmentQuestions.Add(new AssessmentQuestions()
                    {
                        Id = Guid.NewGuid(),
                        QuestionId = newQuestion.Id,
                        AssessmentId = newAssessment.Id,
                        IsEnabled = true
                    });
                }
            }

            await _db.SaveChangesAsync();

            return newAssessment.Id;
        }

        public async Task<bool> UpdateAssessment(AssessmentIn assessment)
        {
            var existingAssessment = await _db.Assessments
                .Where(x => x.Id == assessment.Id)
                .Include(x => x.AssessmentQuestions)
                    .ThenInclude(x => x.UserAnswers)
                .FirstOrDefaultAsync();

            if (existingAssessment == null)
            {
                return false;
            }

            existingAssessment.AssessmentTypeId = assessment.TypeId;
            existingAssessment.CoverMediaUrl = assessment.CoverMediaUrl;
            existingAssessment.RequiredQuestions = assessment.RequiredQuestions;
            existingAssessment.UpgradeMessage = assessment.UpgradeMessage;
            existingAssessment.IsRetake = assessment.IsRetake;

            var newAssessmentQuestions = assessment.QuestionIds
                .Where(x => !existingAssessment.AssessmentQuestions.Select(a => a.QuestionId).Contains(x))
                .Select(x => new AssessmentQuestions()
                {
                    Id = Guid.NewGuid(),
                    QuestionId = x,
                    AssessmentId = existingAssessment.Id,
                    IsEnabled = true
                })
                .AsEnumerable();

            if (newAssessmentQuestions != null && newAssessmentQuestions.Any())
            {
                _db.AssessmentQuestions.AddRange(newAssessmentQuestions);
            }

            var assessmentQuestionsToDelete = existingAssessment.AssessmentQuestions
                .Where(x => !assessment.QuestionIds.Contains(x.QuestionId) && !x.UserAnswers.Any())
                .AsEnumerable();

            if (assessmentQuestionsToDelete != null && assessmentQuestionsToDelete.Any())
            {
                _db.AssessmentQuestions.RemoveRange(assessmentQuestionsToDelete);
            }

            await _db.SaveChangesAsync();

            return true;
        }

        public async Task<bool> UpdateAssessmentQuestion(Guid assessmentId, AssessmentQuestionIn assessmentQuestion)
        {
            var existingAssessmentQuestion = await _db.AssessmentQuestions
                .Where(x => x.Id == assessmentQuestion.Id && x.AssessmentId == assessmentId)
                .Include(x => x.Question)
                .FirstOrDefaultAsync();

            if (existingAssessmentQuestion == null)
            {
                return false;
            }

            existingAssessmentQuestion.IsEnabled = assessmentQuestion.IsEnabled;
            existingAssessmentQuestion.Question.Weighting = assessmentQuestion.Question.Weighting;
            existingAssessmentQuestion.Question.Required = assessmentQuestion.Question.Required;

            await _db.SaveChangesAsync();

            return true;
        }

        public async Task<Guid> CreateQuestion(QuestionIn question, bool isKnowledge = false)
        {
            var newQuestion = new Questions()
            {
                Id = Guid.NewGuid(),
                QuestionTypeId = question.QuestionTypeId,
                Purpose = isKnowledge == true ? "Knowledge" : null,
                Weighting = question.Weighting
            };

            _db.Questions.Add(newQuestion);
            await _db.SaveChangesAsync();

            return newQuestion.Id;
        }

        public async Task<bool> UpdateQuestion(QuestionIn question, Guid? instanceId)
        {
            var existingQuestion = await _db.Questions
                .Include(x => x.QuestionAnswers)
                .ThenInclude(x => x.UserQuestionAnswers)
                .Where(x => x.Id == question.Id)
                .FirstOrDefaultAsync();

            if (existingQuestion == null)
            {
                return false;
            }

            existingQuestion.Title = question.Title;
            existingQuestion.Runtime = question.Runtime;
            existingQuestion.QuestionTypeId = question.QuestionTypeId;
            existingQuestion.QuestionText = question.QuestionText;
            existingQuestion.QuestionDescription = question.QuestionDescription;
            existingQuestion.Weighting = question.Weighting;
            existingQuestion.Required = question.Required;
            existingQuestion.SortOrder = question.SortOrder;
            existingQuestion.IsDynamicOptions = question.IsDynamicOptions;
            existingQuestion.IsAuthorEditable = question.IsAuthorEditable;
            existingQuestion.IsRandom = question.IsRandom;
            existingQuestion.IsOtherOption = question.IsOtherOption;
            existingQuestion.IsLimitTo = question.IsLimitTo;
            existingQuestion.Limit = question.Limit;
            existingQuestion.IsOptionPhoto = question.IsOptionPhoto;
            existingQuestion.ScaleStart = question.ScaleStart;
            existingQuestion.ScaleEnd = question.ScaleEnd;
            existingQuestion.ScaleLabelOne = question.ScaleLabelOne;
            existingQuestion.ScaleLabelTwo = question.ScaleLabelTwo;
            existingQuestion.ScaleLabelThree = question.ScaleLabelThree;
            existingQuestion.BackgroundImageAssetId = question.BackgroundImageAssetId;
            existingQuestion.DynamicOptionsParentId = question.DynamicOptionsParentId;
            existingQuestion.Purpose = question.Purpose;
            existingQuestion.AnswerText = question.AnswerText;
            existingQuestion.MeasuredTagId = question.MeasuredTagId;

            var answersToDelete = existingQuestion.QuestionAnswers
                .Where(x => !question.QuestionAnswers.Select(a => a.Id).Contains(x.Id) && x.InstanceId == instanceId)
                .AsEnumerable();

            if (answersToDelete != null && answersToDelete.Any())
            {
                _db.QuestionAnswers.RemoveRange(answersToDelete);
            }

            if (existingQuestion.QuestionAnswers.Any())
            {
                foreach (var answer in existingQuestion.QuestionAnswers.Where(x => x.InstanceId == instanceId))
                {
                    var answerIncomming = question.QuestionAnswers.Where(x => x.Id == answer.Id).FirstOrDefault();

                    if (answerIncomming != null)
                    {
                        answer.IsCorrect = answerIncomming.IsCorrect;
                        answer.Title = answerIncomming.Title;
                        answer.AssetId = answerIncomming.AssetId;
                        answer.TagId = answerIncomming.TagId;
                    }
                }
            }

            var newQuestionAnswers = question.QuestionAnswers
                .Where(x => x.Id == null || !existingQuestion.QuestionAnswers.Any(y => y.Id == x.Id && y.InstanceId == instanceId))
                .Select(x => new QuestionAnswers()
                {
                    Id = Guid.NewGuid(),
                    QuestionId = x.QuestionId,
                    Title = x.Title,
                    Value = x.Value,
                    SortOrder = x.SortOrder,
                    AssetId = x.AssetId,
                    IsCorrect = x.IsCorrect,
                    InstanceId = instanceId,
                    TagId = x.TagId
                })
                .AsEnumerable();

            if (newQuestionAnswers != null && newQuestionAnswers.Any())
            {
                _db.QuestionAnswers.AddRange(newQuestionAnswers);
            }

            await _db.SaveChangesAsync();

            return true;
        }

        public async Task<bool> SetAssessmentQuestionSortOrderDirect(Guid assessmentId, IEnumerable<AssessmentQuestionLiteIn> assessmentQuestionsIn)
        {
            var assesmentQuestions = await _db.AssessmentQuestions
                .Where(x => x.AssessmentId == assessmentId)
                .ToListAsync();

            if (assesmentQuestions == null)
            {
                return false;
            }

            //Update Selected And All Nulls With New List.
            foreach (var question in assesmentQuestions)
            {
                question.SortOrder = assessmentQuestionsIn
                    .Where(x => x.Id == question.Id)
                    .Select(x => x.SortOrder)
                    .FirstOrDefault();
            }

            await _db.SaveChangesAsync();

            return true;
        }

        public async Task<PagedResult<AssessmentQuestion>> GetQuestionManagerDashboard(Guid userId, string search, int pageNo, int pageSize)
        {
            var skip = pageNo * pageSize;

            var assessmentQuestions = _db.AssessmentQuestions
                .Include(x => x.Question)
                    .ThenInclude(x => x.QuestionType)
                .AsQueryable();

            if (!string.IsNullOrEmpty(search))
            {
                assessmentQuestions = assessmentQuestions.Where(x => x.Question.Title.Contains(search) || x.Question.QuestionType.Name.Contains(search) || (x.Question.Category != null && x.Question.CategoryNavigation.Name.Contains(search)));
            }

            var total = await assessmentQuestions.CountAsync();

            var data = await assessmentQuestions
                .Skip(skip)
                .Take(pageSize)
                .ToListAsync();

            return new PagedResult<AssessmentQuestion>(data.Select(x => new AssessmentQuestion(x)), pageNo, pageSize, total);
        }

        public async Task<UserAnswer?> AddUserAnswer(UserAnswerIn userAnswer, Guid userId)
        {
            var existingUserAnswer = await _db.UserAnswers
                                    .Where(x => x.InstanceId == userAnswer.InstanceId && x.UserId == userId && x.QuestionId == userAnswer.QuestionId)
                                    .FirstOrDefaultAsync();

            if (existingUserAnswer != null)
            {
                userAnswer.Id = existingUserAnswer.Id;
                userAnswer.UserQuestionAnswers = userAnswer.UserQuestionAnswers.Where(x => x.QuestionAnswerId != null).Select(x => new UserQuestionAnswerIn()
                {
                    Id = null,
                    UserAnswerId = userAnswer.Id,
                    QuestionAnswerId = x.QuestionAnswerId,
                    SortOrder = x.SortOrder
                });

                var ua = await UpdateUserAnswer(userAnswer);
                return ua;
            }

            var newUserAnswer = new UserAnswers()
            {
                Id = Guid.NewGuid(),
                InstanceId = userAnswer.InstanceId,
                UserId = userId,
                AnswerText = userAnswer.AnswerText,
                QuestionId = userAnswer.QuestionId,
                InstanceSectionComponentId = userAnswer.InstanceComponentId
            };

            _db.UserAnswers.Add(newUserAnswer);

            if (userAnswer.UserQuestionAnswers != null && userAnswer.UserQuestionAnswers.Any())
            {
                var newQuestionAnswers = userAnswer.UserQuestionAnswers.Where(x => x.QuestionAnswerId != null).Select(x => new UserQuestionAnswers()
                {
                    Id = Guid.NewGuid(),
                    UserAnswerId = newUserAnswer.Id,
                    QuestionAnswerId = x.QuestionAnswerId,
                    TagId = x.TagId,
                    SortOrder = x.SortOrder
                });

                _db.UserQuestionAnswers.AddRange(newQuestionAnswers);
            }

            await _db.SaveChangesAsync();

            var answer = await _db.UserAnswers
                .Include(x => x.UserQuestionAnswers)
                .Where(x => x.Id == newUserAnswer.Id)
                .FirstOrDefaultAsync();

            return new UserAnswer(answer, answer.InstanceSectionComponentId);
        }

        public async Task<UserAnswer?> UpdateUserAnswer(UserAnswerIn userAnswer)
        {
            var existingUserAnswer = await _db.UserAnswers
                .Include(x => x.UserQuestionAnswers)
                .Include(x => x.Question)
                    .ThenInclude(x => x.QuestionType)
                .Where(x => x.Id == userAnswer.Id)
                .FirstOrDefaultAsync();

            if (existingUserAnswer == null)
            {
                return null;
            }

            existingUserAnswer.AnswerText = userAnswer.AnswerText;
            existingUserAnswer.Marks = userAnswer.Marks;

            // Check question type and use provided percentage for certain types, similar to frontend logic
            var questionTypeName = existingUserAnswer?.Question?.QuestionType?.Name;
            bool shouldUseProvidedPercentage = questionTypeName == "File Upload" ||
                                             questionTypeName == "Multiple Choice" ||
                                             questionTypeName == "Picture Choice" ||
                                             questionTypeName == "Short Answer";

            if (shouldUseProvidedPercentage && userAnswer.GradePercentage.HasValue)
            {
                // Use the provided grade percentage from frontend
                existingUserAnswer.GradePercentage = userAnswer.GradePercentage.Value;

                // Update marks to be consistent with the provided grade percentage, but only if weighting is available
                if (existingUserAnswer?.Question?.Weighting > 0)
                {
                    // Calculate marks as a proportion of the weighting based on grade percentage
                    existingUserAnswer.Marks = Convert.ToInt32((userAnswer.GradePercentage.Value / 100m) * existingUserAnswer.Question.Weighting.Value);
                }
            }
            else
            {
                // Calculate percentage for auto-gradable questions or when no percentage provided
                decimal gradePercentage = existingUserAnswer?.Question?.Weighting > 0 && userAnswer.Marks <= existingUserAnswer?.Question?.Weighting ? Convert.ToDecimal(userAnswer.Marks / existingUserAnswer.Question.Weighting) * 100 : 100;
                existingUserAnswer.GradePercentage = Convert.ToInt32(Math.Round(gradePercentage, MidpointRounding.ToEven));
            }

            existingUserAnswer.IsEducatorGraded = userAnswer.IsEducatorGraded;
            existingUserAnswer.AnswerStatusTypeBw = userAnswer.AnswerStatusTypeBw;

            if (userAnswer.UserQuestionAnswers != null && userAnswer.UserQuestionAnswers.Any() && userAnswer.IsEducatorGraded != true)
            {
                var newUserQuestionAnswers = userAnswer.UserQuestionAnswers
                    .Where(x => !existingUserAnswer.UserQuestionAnswers.Any(y => (y.QuestionAnswerId != null && y.QuestionAnswerId == x.QuestionAnswerId) || (y.QuestionAnswerId == null && y.TagId == x.TagId)))
                    .Select(x => new UserQuestionAnswers()
                    {
                        Id = Guid.NewGuid(),
                        UserAnswerId = existingUserAnswer.Id,
                        QuestionAnswerId = x.QuestionAnswerId,
                        TagId = x.TagId,
                        SortOrder = x.SortOrder
                    })
                    .AsEnumerable();

                if (newUserQuestionAnswers != null && newUserQuestionAnswers.Any())
                {
                    _db.UserQuestionAnswers.AddRange(newUserQuestionAnswers);
                }

                var answersToDelete = existingUserAnswer.UserQuestionAnswers
                    .Where(x => !userAnswer.UserQuestionAnswers.Any(y => (y.QuestionAnswerId != null && y.QuestionAnswerId == x.QuestionAnswerId) || (y.QuestionAnswerId == null && y.TagId == x.TagId)))
                    .AsEnumerable();

                if (answersToDelete != null && answersToDelete.Any())
                {
                    _db.UserQuestionAnswers.RemoveRange(answersToDelete);
                }
            }
            else if (userAnswer.IsEducatorGraded != true)
            {
                if (existingUserAnswer.UserQuestionAnswers != null && existingUserAnswer.UserQuestionAnswers.Any())
                {
                    _db.UserQuestionAnswers.RemoveRange(existingUserAnswer.UserQuestionAnswers);
                }
            }

            await _db.SaveChangesAsync();

            var answer = await _db.UserAnswers
                .Include(x => x.UserQuestionAnswers)
                .Where(x => x.Id == existingUserAnswer.Id)
                .FirstOrDefaultAsync();

            return new UserAnswer(answer);
        }

        public async Task<bool> UnSubmitAssessment(Guid userId, Guid instanceId)
        {
            var instanceSectionComponentQuestions = await _db.InstanceSectionComponents
                .Include(x => x.InstanceSection)
                .Include(x => x.Component)
                    .ThenInclude(x => x.Question)
                        .ThenInclude(x => x.QuestionAnswers)
                .Include(x => x.Component)
                    .ThenInclude(x => x.Question)
                        .ThenInclude(x => x.UserAnswers)
                            .ThenInclude(x => x.UserQuestionAnswers)
                .Include(x => x.Component)
                    .ThenInclude(x => x.Question)
                        .ThenInclude(x => x.QuestionType)
                .Where(x => x.InstanceSection.InstanceId == instanceId && x.Component.QuestionId != null && x.IsDeleted != true)
                .ToListAsync();

            if (instanceSectionComponentQuestions == null || !instanceSectionComponentQuestions.Any())
            {
                return false;
            }

            List<Questions?> instanceQuestions = instanceSectionComponentQuestions.Select(x => x.Component.Question).ToList();

            var userAnswers = await GetUserAnswers(userId, instanceQuestions.Select(x => x.Id).ToList(), instanceId);
            var totalMarks = 0;

            if (instanceQuestions != null && instanceQuestions.Any())
            {
                foreach (var question in instanceQuestions)
                {
                    var userAnswer = userAnswers.Where(x => x.QuestionId == question.Id).FirstOrDefault();

                    if (userAnswer == null)
                    {
                        continue;
                    }

                    userAnswer.IsSubmitted = false;
                }
            }

            await _db.SaveChangesAsync();

            return true;
        }

        public async Task<UserAnswer> UnSubmitQuestion(Guid userId, Guid instanceId, Guid questionId)
        {
            var instanceSectionComponentQuestion = await _db.InstanceSectionComponents
                .Include(x => x.InstanceSection)
                .Include(x => x.Component)
                    .ThenInclude(x => x.Question)
                        .ThenInclude(x => x.QuestionAnswers)
                .Include(x => x.Component)
                    .ThenInclude(x => x.Question)
                        .ThenInclude(x => x.UserAnswers)
                            .ThenInclude(x => x.UserQuestionAnswers)
                .Include(x => x.Component)
                    .ThenInclude(x => x.Question)
                        .ThenInclude(x => x.QuestionType)
                .Where(x => x.InstanceSection.InstanceId == instanceId && x.Component.QuestionId != null && x.Component.QuestionId == questionId && x.IsDeleted != true)
                .FirstOrDefaultAsync();

            if (instanceSectionComponentQuestion == null)
            {
                return null;
            }

            Questions instanceQuestion = instanceSectionComponentQuestion.Component.Question;

            var userAnswers = await GetUserAnswers(userId, new List<Guid>() { instanceQuestion.Id }, instanceId);
            var userAnswer = userAnswers.Where(x => x.QuestionId == instanceQuestion.Id).FirstOrDefault();

            if (instanceQuestion != null)
            {
                if (userAnswer == null)
                {
                    return null;
                }

                userAnswer.IsSubmitted = false;
            }

            await _db.SaveChangesAsync();

            return new UserAnswer(userAnswer, instanceSectionComponentQuestion.Id);
        }

        public async Task<UserAnswer> SubmitQuestion(Guid userId, Guid instanceId, Guid questionId)
        {
            var instanceSectionComponentQuestion = await _db.InstanceSectionComponents
                .Include(x => x.InstanceSection)
                .Include(x => x.Component)
                    .ThenInclude(x => x.Question)
                        .ThenInclude(x => x.QuestionAnswers)
                .Include(x => x.Component)
                    .ThenInclude(x => x.Question)
                        .ThenInclude(x => x.QuestionType)
                .Where(x => x.InstanceSection.InstanceId == instanceId && x.Component.QuestionId != null && x.Component.QuestionId == questionId && x.IsDeleted != true)
                .FirstOrDefaultAsync();

            List<Guid> markedQuestionIds = new List<Guid>();

            if (instanceSectionComponentQuestion == null)
            {
                return null;
            }

            Questions instanceQuestion = instanceSectionComponentQuestion.Component.Question;

            var userAnswers = await GetUserAnswers(userId, new List<Guid>() { instanceQuestion.Id }, instanceId);
            var userAnswer = userAnswers.FirstOrDefault();

            if (instanceQuestion != null)
            {
                if (userAnswer == null)
                {
                    return null;
                }

                var totalCorrectAnswers = 0;
                var userTotalAnswersCorrect = 0;

                // All the question types that can be auto graded.
                switch (instanceQuestion.QuestionType.Name)
                {
                    case "Matching List":
                    case "Picture Matching":
                    case "Text to Picture":
                    case "Multiple Choice":
                    case "Dropdown":
                    case "True/False":
                    case "Yes/No":
                        markedQuestionIds.Add(instanceQuestion.Id);
                        break;
                }

                if (instanceQuestion.QuestionType.Name == "Matching List" ||
                    instanceQuestion.QuestionType.Name == "Picture Matching" ||
                    instanceQuestion.QuestionType.Name == "Text to Picture"
                    )
                {
                    var questionAnswers = instanceQuestion.QuestionAnswers.First(x => x.InstanceId == instanceId).Value;
                    var matchingItemPrompts = questionAnswers.Deserialize<MatchingItem>();
                    totalCorrectAnswers = matchingItemPrompts.ItemPrompts.Count;

                    var promptAnswers = userAnswer.AnswerText.Deserialize<List<ItemPrompt>>();
                    userTotalAnswersCorrect = promptAnswers.Where(x => x.Matched == true).Count();
                }
                else
                {
                    totalCorrectAnswers = instanceQuestion.QuestionType.Name == "Multiple Choice" || instanceQuestion.QuestionType.Name == "Dropdown" && (instanceQuestion.QuestionType.Name != "True/False" && instanceQuestion.QuestionType.Name != "Yes/No")
                        ? instanceQuestion.QuestionAnswers.Where(x => x.IsCorrect == true && x.InstanceId == instanceId).Count()
                        : instanceQuestion.QuestionAnswers.Where(x => x.IsCorrect == true).Count();

                    userTotalAnswersCorrect = userAnswer.UserQuestionAnswers.Where(x => x.QuestionAnswer != null && x.QuestionAnswer.IsCorrect == true).Count();

                }


                decimal gradePercentage = totalCorrectAnswers > 0 ? (userTotalAnswersCorrect / totalCorrectAnswers) * 100 : 100;
                userAnswer.GradePercentage = Convert.ToInt32(Math.Round(gradePercentage, MidpointRounding.ToEven));
                userAnswer.Marks = userTotalAnswersCorrect == totalCorrectAnswers ? instanceQuestion.Weighting ?? 1 : 0;
                userAnswer.IsSubmitted = true;

                if (markedQuestionIds.Any(x => x == instanceQuestion.Id))
                {
                    userAnswer.IsAutoGraded = true;

                    if (userTotalAnswersCorrect == totalCorrectAnswers)
                    {
                        userAnswer.AnswerStatusTypeBw = (int)AnswerStatusTypes.Correct;
                    }
                    else if (totalCorrectAnswers > 0 && userTotalAnswersCorrect > 0)
                    {
                        userAnswer.AnswerStatusTypeBw = (int)AnswerStatusTypes.PartiallyCorrect;
                    }
                    else
                    {
                        userAnswer.AnswerStatusTypeBw = (int)AnswerStatusTypes.Incorrect;
                    }
                }

                userAnswer.IsSubmitted = true;
            }

            await _db.SaveChangesAsync();

            return new UserAnswer(userAnswer, instanceSectionComponentQuestion.Id);
        }

        public async Task<AssessmentSubmitResult> SubmitAssessment(Guid userId, Guid instanceId)
        {
            var instanceSectionComponentQuestions = await _db.InstanceSectionComponents
                .Include(x => x.InstanceSection)
                .Include(x => x.Component)
                    .ThenInclude(x => x.Question)
                        .ThenInclude(x => x.QuestionAnswers)
                .Include(x => x.Component)
                    .ThenInclude(x => x.Question)
                        .ThenInclude(x => x.UserAnswers)
                            .ThenInclude(x => x.UserQuestionAnswers)
                .Include(x => x.Component)
                    .ThenInclude(x => x.Question)
                        .ThenInclude(x => x.QuestionType)
                .Where(x => x.InstanceSection.InstanceId == instanceId && x.Component.QuestionId != null && x.IsDeleted != true)
                .ToListAsync();

            List<Guid> markedQuestionIds = new List<Guid>();

            if (instanceSectionComponentQuestions == null || !instanceSectionComponentQuestions.Any())
            {
                return new AssessmentSubmitResult(instanceId, false, null, null);
            }

            List<Questions?> instanceQuestions = instanceSectionComponentQuestions.Select(x => x.Component.Question).ToList();

            var userAnswers = await GetUserAnswers(userId, instanceQuestions.Select(x => x.Id).ToList(), instanceId);
            var totalMarks = 0;
            var requiredQuestionIds = new List<Guid>();

            if (instanceQuestions != null && instanceQuestions.Any())
            {
                foreach (var question in instanceQuestions)
                {
                    var userAnswer = userAnswers.Where(x => x.QuestionId == question.Id).FirstOrDefault();

                    if (userAnswer == null)
                    {
                        continue;
                    }

                    if (userAnswer.AnswerStatusTypeBw != null)
                    {
                        if (question.Required == true)
                        {
                            totalMarks += question.Weighting ?? 1;
                            requiredQuestionIds.Add(question.Id);
                        }
                        continue;
                    }
                    if (question.Required == true)
                    {
                        totalMarks += question.Weighting ?? 1;
                        requiredQuestionIds.Add(question.Id);
                    }

                    var totalCorrectAnswers = 0;
                    var userTotalAnswersCorrect = 0;

                    // All the question types that can be auto graded.
                    switch (question.QuestionType.Name)
                    {
                        case "Matching List":
                        case "Picture Matching":
                        case "Text to Picture":
                        case "Multiple Choice":
                        case "Dropdown":
                        case "True/False":
                        case "Yes/No":
                            markedQuestionIds.Add(question.Id);
                            break;
                    }

                    if (question.QuestionType.Name == "Matching List" ||
                        question.QuestionType.Name == "Picture Matching" ||
                        question.QuestionType.Name == "Text to Picture"
                        )
                    {
                        var questionAnswers = question.QuestionAnswers.First(x => x.InstanceId == instanceId).Value;
                        var matchingItemPrompts = questionAnswers.Deserialize<MatchingItem>();
                        totalCorrectAnswers = matchingItemPrompts.ItemPrompts.Count;

                        var promptAnswers = userAnswer.AnswerText.Deserialize<List<ItemPrompt>>();
                        userTotalAnswersCorrect = promptAnswers.Where(x => x.Matched == true).Count();
                    }
                    else
                    {
                        totalCorrectAnswers = question.QuestionType.Name == "Multiple Choice" || question.QuestionType.Name == "Dropdown" && (question.QuestionType.Name != "True/False" && question.QuestionType.Name != "Yes/No")
                            ? question.QuestionAnswers.Where(x => x.IsCorrect == true && x.InstanceId == instanceId).Count()
                            : question.QuestionAnswers.Where(x => x.IsCorrect == true).Count();

                        userTotalAnswersCorrect = userAnswer.UserQuestionAnswers.Where(x => x.QuestionAnswer != null && x.QuestionAnswer.IsCorrect == true).Count();

                    }


                    decimal gradePercentage = totalCorrectAnswers > 0 ? (userTotalAnswersCorrect / totalCorrectAnswers) * 100 : 100;
                    userAnswer.GradePercentage = Convert.ToInt32(Math.Round(gradePercentage, MidpointRounding.ToEven));
                    userAnswer.Marks = userTotalAnswersCorrect == totalCorrectAnswers ? question.Weighting ?? 1 : 0;

                    if (markedQuestionIds.Any(x => x == question.Id))
                    {
                        userAnswer.IsAutoGraded = true;

                        if (userTotalAnswersCorrect == totalCorrectAnswers)
                        {
                            userAnswer.AnswerStatusTypeBw = (int)AnswerStatusTypes.Correct;
                        }
                        else if (totalCorrectAnswers > 0 && userTotalAnswersCorrect > 0)
                        {
                            userAnswer.AnswerStatusTypeBw = (int)AnswerStatusTypes.PartiallyCorrect;
                        }
                        else
                        {
                            userAnswer.AnswerStatusTypeBw = (int)AnswerStatusTypes.Incorrect;
                        }
                    }

                    userAnswer.IsSubmitted = true;
                }
            }

            await _db.SaveChangesAsync();

            return new AssessmentSubmitResult(instanceId, true, 0, userAnswers.Select(x => new UserAnswer(x)).ToList());
        }

        public async Task UpdateUserProgress(Guid userId, Guid instanceId, decimal grade)
        {
            await _db.UserProgress.Where(x => x.InstanceId == instanceId && x.UserId == userId).FirstOrDefaultAsync();
        }

        public async Task<List<InstanceQuestionLite>?> GetAssessmentInstanceComponentValue(Guid instanceId, Guid? componentId)
        {
            var instanceComponent = await _db.InstanceSectionComponents.Where(x => x.InstanceSection.InstanceId == instanceId && x.ComponentId == componentId).FirstOrDefaultAsync();

            if (instanceComponent != null && instanceComponent.Value != null && instanceComponent.Value.Length > 0)
            {
                List<InstanceQuestionLite> instanceQuestionsLite = new List<InstanceQuestionLite>();

                if (instanceComponent.Value.IndexOf("[{") == -1 && instanceComponent.Value.IndexOf("\"") == -1 && instanceComponent.Value != "[]")
                {
                    instanceQuestionsLite = instanceComponent.Value.Split(';').Select(x => new InstanceQuestionLite() { Id = Guid.Parse(x), SortOrder = 0 }).ToList();
                }
                else if (instanceComponent.Value != null && instanceComponent.Value.Length > 0 && instanceComponent.Value != "[]")
                {
                    instanceQuestionsLite = JsonSerializer.Deserialize<List<InstanceQuestionLite>>(instanceComponent.Value);
                }

                return instanceQuestionsLite;
            }

            return null;
        }

        public async Task<bool> UpdateUserQuestionFeedback(Guid userQuestionAnswerId, Guid userId, string feedback)
        {
            var existingUserAnswer = await _db.UserAnswers
                .Where(x => x.Id == userQuestionAnswerId)
                .FirstOrDefaultAsync();

            if (existingUserAnswer != null)
            {
                existingUserAnswer.Feedback = feedback;
                existingUserAnswer.IsEducatorGraded = true;
                //existingUserAnswer.EducatorId = userId;
            }

            await _db.SaveChangesAsync();

            return true;
        }

        public async Task<List<UserAnswers>>? GetUserAnswers(Guid userId, List<Guid>? questionIds, Guid? instanceId)
        {
            var userAnswers = await _db.UserAnswers
                .Include(x => x.UserQuestionAnswers)
                    .ThenInclude(x => x.QuestionAnswer)
                        .ThenInclude(x => x.Tag)
                .Include(x => x.Question)
                .Where(x => x.UserId == userId && x.InstanceId == instanceId && x.QuestionId != null && questionIds.Contains((Guid)x.QuestionId)).ToListAsync();

            return userAnswers;
        }

        public async Task CalculateUserRiasecLetterScore(Guid userId, Guid questionId, int score, Guid? tagId, Guid? userAnswerId, CancellationToken token)
        {
            // BASELINE -------------------------------------------------------------------------------
            var existingBaselineUserQuestionAnswerIds = await _db.UserQuestionAnswers.Include(x => x.UserAnswer).Where(x => x.QuestionAnswer.QuestionId == questionId && x.UserAnswer.UserId == userId).Select(x => x.TagId).ToListAsync();
            var baselineQuestionScoresToRemove = await _db.UserRiasecAssessmentScores.Where(x => x.QuestionId == questionId && !existingBaselineUserQuestionAnswerIds.Contains((Guid)x.RiasecTagId)).ToListAsync();

            if (baselineQuestionScoresToRemove.Count > 0)
            {
                _db.UserRiasecAssessmentScores.RemoveRange(baselineQuestionScoresToRemove);
                await _db.SaveChangesAsync();
            }

            var userRiasecScore = await _db.UserRiasecAssessmentScores.Where(x => x.UserId == userId && x.QuestionId == questionId && x.RiasecTagId == tagId).FirstOrDefaultAsync();

            if (userRiasecScore == null)
            {
                var newRiasecScore = new UserRiasecAssessmentScores()
                {
                    Id = Guid.NewGuid(),
                    UserId = userId,
                    RiasecTagId = tagId,
                    Score = score,
                    QuestionId = questionId
                };

                _db.UserRiasecAssessmentScores.Add(newRiasecScore);
            }
            else
            {
                userRiasecScore.Score = score;
                userRiasecScore.RiasecTagId = tagId;
            }

            // DYNAMIC ---------------------------------------------------------------------------------
            if (userAnswerId != null)
            {
                tagId = await _db.UserQuestionAnswers
                    .Where(x => x.UserAnswerId == userAnswerId)
                    .Select(x => x.TagId).FirstOrDefaultAsync();

                score = await _db.Questions
                    .Where(x => x.Id == questionId)
                    .Select(x => x.Weighting ?? 3)
                    .FirstOrDefaultAsync();

                var existingUserQuestionAnswerIds = await _db.UserQuestionAnswers.Include(x => x.UserAnswer).Where(x => x.QuestionAnswer.QuestionId == questionId && x.UserAnswer.UserId == userId).Select(x => x.TagId).ToListAsync();
                var questionScoresToRemove = await _db.EfdynamicUserRiasecAssessmentScores.Where(x => x.QuestionId == questionId && !existingUserQuestionAnswerIds.Contains((Guid)x.RiasecTagId)).ToListAsync();

                if (questionScoresToRemove.Count > 0)
                {
                    _db.EfdynamicUserRiasecAssessmentScores.RemoveRange(questionScoresToRemove);
                    await _db.SaveChangesAsync();
                }
            }

            var dynamicUserRiasecScore = await _db.EfdynamicUserRiasecAssessmentScores.Where(x => x.UserId == userId && x.QuestionId == questionId && x.RiasecTagId == tagId).FirstOrDefaultAsync();

            if (dynamicUserRiasecScore == null)
            {
                var newDynamicRiasecScore = new EfdynamicUserRiasecAssessmentScores()
                {
                    Id = Guid.NewGuid(),
                    UserId = userId,
                    RiasecTagId = tagId,
                    Score = score,
                    QuestionId = questionId
                };

                _db.EfdynamicUserRiasecAssessmentScores.Add(newDynamicRiasecScore);
            }
            else
            {
                dynamicUserRiasecScore.Score = score;
            }

            await _db.SaveChangesAsync();

            // BASELINE -------------------------------------------------------------------------------
            await _db.LoadStoredProc("SetUserFinalRiasecScore")
                .WithSqlParam("@userId", userId)
                .ExecuteStoredProcAsync(token);
            // DYNAMIC ---------------------------------------------------------------------------------
            await _db.LoadStoredProc("SetEFDynamicUserFinalRiasecScore")
                .WithSqlParam("@userId", userId)
                .ExecuteStoredProcAsync(token);
        }

        public async Task CalculateEfDynamicUserRiasecLetterScore(Guid userId, Guid questionId, int score, Guid? tagId, Guid? userAnswerId, CancellationToken token)
        {
            if (userAnswerId != null)
            {
                tagId = await _db.UserQuestionAnswers
                    .Where(x => x.UserAnswerId == userAnswerId)
                    .Select(x => x.TagId).FirstOrDefaultAsync();

                score = await _db.Questions
                    .Where(x => x.Id == questionId)
                    .Select(x => x.Weighting ?? 3)
                    .FirstOrDefaultAsync();

                var existingUserQuestionAnswerIds = await _db.UserQuestionAnswers.Include(x => x.UserAnswer).Where(x => x.QuestionAnswer.QuestionId == questionId && x.UserAnswer.UserId == userId).Select(x => x.TagId).ToListAsync();
                var questionScoresToRemove = await _db.EfdynamicUserRiasecAssessmentScores.Where(x => x.QuestionId == questionId && !existingUserQuestionAnswerIds.Contains((Guid)x.RiasecTagId)).ToListAsync();

                if (questionScoresToRemove.Count > 0)
                {
                    _db.EfdynamicUserRiasecAssessmentScores.RemoveRange(questionScoresToRemove);
                    await _db.SaveChangesAsync();
                }
            }

            var dynamicUserRiasecScore = await _db.EfdynamicUserRiasecAssessmentScores.Where(x => x.UserId == userId && x.QuestionId == questionId && x.RiasecTagId == tagId).FirstOrDefaultAsync();

            if (dynamicUserRiasecScore == null)
            {
                var newDynamicRiasecScore = new EfdynamicUserRiasecAssessmentScores()
                {
                    Id = Guid.NewGuid(),
                    UserId = userId,
                    RiasecTagId = tagId,
                    Score = score,
                    QuestionId = questionId
                };

                _db.EfdynamicUserRiasecAssessmentScores.Add(newDynamicRiasecScore);
            }
            else
            {
                dynamicUserRiasecScore.Score = score;
            }

            await _db.SaveChangesAsync();

            await _db.LoadStoredProc("SetEFDynamicUserFinalRiasecScore")
                .WithSqlParam("@userId", userId)
                .ExecuteStoredProcAsync(token);
        }

        public async Task ResetRiasecScore(Guid userId, CancellationToken token)
        {
            var UserRiasecAssessmentScores = await _db.UserRiasecAssessmentScores.Where(x => x.UserId == userId).ToListAsync();

            if (UserRiasecAssessmentScores != null && UserRiasecAssessmentScores.Any())
            {
                _db.UserRiasecAssessmentScores.RemoveRange(UserRiasecAssessmentScores);
            }

            var userRiasecFinalScores = await _db.UserRiasecFinalScores.Where(x => x.UserId == userId).ToListAsync();

            if (userRiasecFinalScores != null && userRiasecFinalScores.Any())
            {
                _db.UserRiasecFinalScores.RemoveRange(userRiasecFinalScores);
            }

            var dynamicScoreItemsToRemove = await _db.EfdynamicUserRiasecAssessmentScores.Where(x => x.UserId == userId).ToListAsync();

            if (dynamicScoreItemsToRemove != null && dynamicScoreItemsToRemove.Any())
            {
                _db.EfdynamicUserRiasecAssessmentScores.RemoveRange(dynamicScoreItemsToRemove);
            }

            var userDynamicRiasecFinalScores = await _db.EfdynamicUserRiasecFinalScores.Where(x => x.UserId == userId).ToListAsync();

            if (userDynamicRiasecFinalScores != null && userDynamicRiasecFinalScores.Any())
            {
                _db.EfdynamicUserRiasecFinalScores.RemoveRange(userDynamicRiasecFinalScores);
            }

            await _db.SaveChangesAsync();

            await _db.LoadStoredProc("SetUserFinalRiasecScore")
                .WithSqlParam("@userId", userId)
                .ExecuteStoredProcAsync(token);
        }

        public async Task<CombinedUserRiasecFinalScore> GetUserRiasecFinalScore(Guid userId)
        {
            var score = await _db.UserRiasecFinalScores.Where(x => x.UserId == userId).FirstOrDefaultAsync();
            var efDynamicRiasecScore = await _db.EfdynamicUserRiasecFinalScores.Where(x => x.UserId == userId).FirstOrDefaultAsync();

            return new CombinedUserRiasecFinalScore(score != null ? new UserRiasecFinalScore(score) : new UserRiasecFinalScore(efDynamicRiasecScore), new UserRiasecFinalScore(efDynamicRiasecScore));
        }
        #endregion
    }
}
